#include <pybind11/functional.h>
#include <pybind11/pybind11.h>

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <iostream>
#include <mutex>
#include <queue>
#include <string>
#include <thread>

namespace py = pybind11;

// CPP 核心逻辑
class MessageReceiver {
   public:
    MessageReceiver(py::object py_callback) : stop_flag(false), callback(py_callback) {}

    void start() {
        worker = std::thread([this]() {
            int count = 0;
            while (!stop_flag) {
                // 模拟外部接收消息
                std::this_thread::sleep_for(std::chrono::seconds(1));
                std::string msg = "msg_" + std::to_string(count++);
                // 通过回调将消息发回 Python
                py::gil_scoped_acquire acquire;  // 必须获得GIL
                callback(msg);
            }
            std::cout << "stopped" << std::endl;
        });
    }

    void stop() {
        stop_flag = true;
        if (worker.joinable()) {
            // Release the GIL before joining to avoid deadlock
            py::gil_scoped_release release;
            worker.join();
            std::cout << "wait stopped" << std::endl;
        }
    } 

    ~MessageReceiver() { stop(); }

   private:
    std::atomic<bool> stop_flag;
    std::thread worker;
    py::object callback;
};

// 模块封装代码
PYBIND11_MODULE(message_module, m) {
    py::class_<MessageReceiver>(m, "MessageReceiver")
        .def(py::init<py::object>())
        .def("start", &MessageReceiver::start)
        .def("stop", &MessageReceiver::stop);
}
