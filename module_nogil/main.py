import threading
import time
from queue import Queue
import message_module

def main():
    # 用于存储 C++ 线程传来的消息
    msg_queue = Queue()

    # 定义 C++ 线程要调用的回调
    def on_message(msg):
        print(f"Received from C++: {msg}")
        msg_queue.put(msg)
    
    # 创建 C++ 后台线程对象
    receiver = message_module.MessageReceiver(on_message)
    receiver.start()

    try:
        for _ in range(5):
            # 主线程处理消息
            try:
                msg = msg_queue.get(timeout=2)
                print(f"Python got message: {msg}")
            except Exception:
                print("No message received within timeout.")
    finally:
        receiver.stop()
        print("Receiver stopped.")

if __name__ == "__main__":
    main()
