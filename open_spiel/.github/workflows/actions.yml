name: build_and_test

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    strategy:
      matrix:
        include:
        # Standard (most current) platforms and versions.
        - os: ubuntu-22.04
          OS_PYTHON_VERSION: "3.10"
          TRAVIS_USE_NOX: 0
          DEFAULT_OPTIONAL_DEPENDENCY: "ON"
          BUILD_SHARED_LIB: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL: ""
        - os: ubuntu-22.04
          OS_PYTHON_VERSION: "3.10"
          TRAVIS_USE_NOX: 0
          DEFAULT_OPTIONAL_DEPENDENCY: "OFF"
          BUILD_SHARED_LIB: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL: ""
        - os: macos-12
          OS_PYTHON_VERSION: "3.9"
          TRAVIS_USE_NOX: 0
          DEFAULT_OPTIONAL_DEPENDENCY: "OFF"
          BUILD_SHARED_LIB: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL: ""
        # Standard or older platforms with older Python versions.
        - os: macos-11
          OS_PYTHON_VERSION: "3.8"
          TRAVIS_USE_NOX: 0
          DEFAULT_OPTIONAL_DEPENDENCY: "OFF"
          BUILD_SHARED_LIB: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL: ""
        # Build and run tests with all optional dependencies, including building a
        # shared library with linkable third party dependencies in place.
        # TODO(author5): update this to Ubuntu 22.04 and Python 3.10 once OR-Tools for 22.04 is released.
        - os: ubuntu-20.04
          OS_PYTHON_VERSION: "3.9"
          DEFAULT_OPTIONAL_DEPENDENCY: "ON"
          TRAVIS_USE_NOX: 0
          BUILD_SHARED_LIB: "ON"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS: "ON"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL: "https://github.com/google/or-tools/releases/download/v9.2/or-tools_amd64_ubuntu-20.04_v9.2.9972.tar.gz"
        # One older platform with oldest Python version on that platform.
        - os: ubuntu-20.04
          OS_PYTHON_VERSION: "3.8"
          TRAVIS_USE_NOX: 0
          DEFAULT_OPTIONAL_DEPENDENCY: "OFF"
          BUILD_SHARED_LIB: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS: "OFF"
          OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL: ""

    runs-on: ${{ matrix.os }}
    env:
      OPEN_SPIEL_ENABLE_JAX: ON
      OPEN_SPIEL_ENABLE_PYTORCH: ON
      OPEN_SPIEL_ENABLE_TENSORFLOW: ON
      OPEN_SPIEL_ENABLE_PYTHON_MISC: ON
      OS_PYTHON_VERSION: ${{ matrix.OS_PYTHON_VERSION }}
      TRAVIS_USE_NOX:  ${{ matrix.TRAVIS_USE_NOX }}
      DEFAULT_OPTIONAL_DEPENDENCY: ${{ matrix.DEFAULT_OPTIONAL_DEPENDENCY }}
      OPEN_SPIEL_BUILD_WITH_JULIA: ${{ matrix.OPEN_SPIEL_BUILD_WITH_JULIA }}
      BUILD_SHARED_LIB:  ${{ matrix.BUILD_SHARED_LIB }}
      OPEN_SPIEL_BUILD_WITH_ORTOOLS:  ${{ matrix.OPEN_SPIEL_BUILD_WITH_ORTOOLS }}
      OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL:  ${{ matrix.OPEN_SPIEL_BUILD_WITH_ORTOOLS_DOWNLOAD_URL }}

    steps:
    - uses: actions/checkout@v2
    - uses: julia-actions/setup-julia@v1
    - name: Ad-hoc fix
      if: ${{ matrix.DEFAULT_OPTIONAL_DEPENDENCY == 'ON' }}
      run: |
        # workaround for https://github.com/deepmind/open_spiel/issues/606
        sudo cp /usr/lib/x86_64-linux-gnu/libstdc++.so.6 $(julia --startup-file=no -e 'using Libdl;print(abspath(joinpath(Libdl.dlpath("libjulia"), "..", "julia")))')
    - name: Install
      run: |
        pwd
        chmod +x install.sh
        ./install.sh
    - name: Build and test
      run: |
        python3 --version
        ./open_spiel/scripts/ci_script.sh
