# Universal Poker support

This has been contributed by den<PERSON><PERSON> in November 2019 (See
https://github.com/deepmind/open_spiel/pull/97), and is available as an optional
dependency. See the
[https://github.com/deepmind/open_spiel/blob/master/docs/install.md](install.md)
for documentation and `open_spiel/scripts/global_variables.sh` to enable this.

This is a wrapper around the Annual Computer Poker Competition bot (ACPC)
environment. See http://www.computerpokercompetition.org/. The code is initially
available at https://github.com/ethansbrown/acpc

Thanks to dennis<PERSON> for contributing this to the community! If it's useful to
you, feel free to help supporting it. It has not been extensively reviewed or
tested by the DeepMind OpenSpiel team.
