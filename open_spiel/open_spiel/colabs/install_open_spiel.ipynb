{"nbformat": 4, "nbformat_minor": 0, "metadata": {"accelerator": "GPU", "colab": {"name": "install_open_spiel.ipynb", "provenance": [{"file_id": "install_open_spiel.ipynb", "timestamp": 1629100659918}], "collapsed_sections": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "cells": [{"cell_type": "code", "metadata": {"id": "odj1Coq5H080"}, "source": ["#@title ##### License { display-mode: \"form\" }\n", "# Copyright 2019 DeepMind Technologies Ltd. All rights reserved.\n", "#\n", "# Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "#     http://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "dOOzDGYAZcW3"}, "source": ["# OpenSpiel\n", "\n", "* This Colab gets you started with installing OpenSpiel and its dependencies.\n", "* OpenSpiel is a framework for reinforcement learning in games.\n", "* The instructions are adapted from [here](https://github.com/deepmind/open_spiel/blob/master/docs/install.md)."]}, {"cell_type": "markdown", "metadata": {"id": "XC6kQBzWahEF"}, "source": ["## Install"]}, {"cell_type": "markdown", "metadata": {"id": "-2_Vbijh4FlZ"}, "source": ["Install OpenSpiel via pip:\n"]}, {"cell_type": "code", "metadata": {"id": "lQc12Xrn4CXU"}, "source": ["!pip install --upgrade open_spiel"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "jUtlXZ8FBnAL"}, "source": ["# It's play time!"]}, {"cell_type": "code", "metadata": {"id": "ewMXCaUw8d9Q"}, "source": ["import numpy as np\n", "import pyspiel\n", "\n", "game = pyspiel.load_game(\"tic_tac_toe\")\n", "state = game.new_initial_state()\n", "\n", "while not state.is_terminal():\n", "  state.apply_action(np.random.choice(state.legal_actions()))\n", "  print(str(state) + '\\n')"], "execution_count": null, "outputs": []}]}