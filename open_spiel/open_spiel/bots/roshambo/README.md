# RoShamBo Bots

Bots from the International RoShamBo Programming Competition, available as an
optional dependency. See `open_spiel/scripts/global_variables.sh` to configure
conditional dependencies and enable this.

The competition was held in 1999 and 2000 by <PERSON><PERSON> Bill<PERSON> at the University of
Alberta. The player pool was seeded with dummy bots, requiring competitive
entrants to exploit sub-optimal play while avoiding getting exploited by other
sophisticated bots. For more information, and all code used in the first
competition, see https://webdocs.cs.ualberta.ca/~darse/rsbpc.html.

No additional code from the second competition was officially released, but
<PERSON><PERSON><PERSON>, author of the winning entrant <PERSON>, independently released
the bot’s source code http://www.mathpuzzle.com/older.htm. Greenberg is also
included as an OpenSpiel bot.
