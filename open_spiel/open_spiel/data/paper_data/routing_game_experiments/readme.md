# Reproducing routing game experiments

To reproduce the experiments done in [*Solving N-player dynamic routing games
with congestion: a mean field approach, Cabannes et.
al.*](https://dl.acm.org/doi/10.5555/3535850.3536033):

1.  If you have not, download [python](https://www.python.org/downloads/) and an
    IDE to run iPython notebook (either [jupyter](https://jupyter.org) or
    [VSCode](https://code.visualstudio.com)).
2.  Install OpenSpiel using
    [pip install open_spiel](https://github.com/deepmind/open_spiel/blob/master/docs/install.md)
    or from
    [source](https://github.com/deepmind/open_spiel/blob/master/docs/install.md#installation-from-source).
3.  Download the
    [`Experiments.ipynb` iPython notebook](https://github.com/deepmind/open_spiel/tree/master/open_spiel/data/paper_data/routing_game_experiments/Experiments.ipynb).
4.  Run the iPython notebook. You might need to download the dependant python
    libraries.

# License

This code is under the Open Spiel license. Please cite the paper [*Solving
N-player dynamic routing games with congestion: a mean field approach, Cabannes
et. al.*](https://dl.acm.org/doi/10.5555/3535850.3536033) when re-using this
code. Feel free to send an <NAME_EMAIL> for any questions.
