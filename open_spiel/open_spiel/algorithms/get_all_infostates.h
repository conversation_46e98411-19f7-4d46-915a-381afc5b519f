// Copyright 2021 DeepMind Technologies Limited
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef OPEN_SPIEL_ALGORITHMS_GET_ALL_INFOSTATES_H_
#define OPEN_SPIEL_ALGORITHMS_GET_ALL_INFOSTATES_H_

#include <memory>
#include <string>
#include <vector>

#include "open_spiel/spiel.h"

namespace open_spiel {
namespace algorithms {

// Get all the information states in the game. Currently works for sequential
// games. Use -1 for the depth_limit to get everything.
std::vector<std::vector<std::string>> GetAllInformationStates(
    const Game& game, int depth_limit = -1);

}  // namespace algorithms
}  // namespace open_spiel

#endif  // OPEN_SPIEL_ALGORITHMS_GET_ALL_INFOSTATES_H_
