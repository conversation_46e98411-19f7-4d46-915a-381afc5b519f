## OpenSpiel Core API Reference

OpenSpiel consists of several core functions and classes. This page acts as a
helpful reminder of how to use the main functionality of OpenSpiel.

Most of the functions are described and illustrated via Python syntax and
examples, and there are pointers to the corresponding C++ functions.

<b><u>Disclaimer</u></b>: This is meant as a guide to facilitate OpenSpiel development
in Python. However,
[spiel.h](https://github.com/deepmind/open_spiel/blob/master/open_spiel/spiel.h)
remains the single source of truth for documentation on the core API.

### Core Functions

Method                                                               | Python                                                       | C++                                                                                                                    | Description
-------------------------------------------------------------------- | ------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------- | -----------
`deserialize_game_and_state(serialized_data: string)`                | [Python](api_reference/game_deserialize_game_and_state.md) | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L1127) | Returns a tuple of (game, state) reconstructed from the serialized object data.
`load_game(game_string: str)`                                        | [Python](api_reference/load_game.md)                       | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L1080) | Returns a game object for the specified game string.
`load_game(game_string: str, parameters: Dict[str, Any])`            | [Python](api_reference/load_game.md)                       | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L1083) | Returns a game object for the specified game string and parameter values.
`registered_names()`                                                 | [Python](api_reference/registered_names.md)                | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L1051) | Returns a list of all short names of games in the library.
`serialize_game_and_state(game: pyspiel.Game, state: pyspiel.State)` | [Python](api_reference/game_serialize_game_and_state.md)   | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L1104) | Returns a string representation of the state and game that created it.

### State methods

Method                                       | Python                                                      | C++                                                                                                                   | Description
-------------------------------------------- | ----------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------- | -----------
`action_to_string(player: int, action: int)` | [Python](api_reference/state_action_to_string.md)         | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L289) | Returns a string representation of the specified player's action.
`apply_action(action: int)`                  | [Python](api_reference/state_apply_action.md)             | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L230) | Applies the specified action to the state.
`apply_actions(actions: List[int])`          | [Python](api_reference/state_apply_actions.md)            | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L581) | Applies the specified joint action (action for each player) to the state.
`chance_outcomes()`                          | [Python](api_reference/state_chance_outcomes.md)          | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L604) | Returns the a list of (action, prob) tuples representing the chance outcome distribution.
`current_player()`                           | [Python](api_reference/state_current_player.md)           | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L225) | Returns the player ID of the acting player.
`history()`                                  | [Python](api_reference/state_history.md)                  | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L406) | Returns the sequence of actions taken by all players since the start of the game.
`information_state_string()`                 | [Python](api_reference/state_information_state_string.md) | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L433) | Returns a string representing the information state for the current player.
`information_state_string(player: int)`      | [Python](api_reference/state_information_state_string.md) | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L433) | Returns a string representing the information state for the specified player.
`information_state_tensor()`                 | [Python](api_reference/state_information_state_tensor.md) | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L488) | Returns a list of floats representing the information state for the current player.
`information_state_tensor(player: int)`      | [Python](api_reference/state_information_state_tensor.md) | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L488) | Returns a list of floats representing the information state for the specified player.
`is_chance_node()`                           | [Python](api_reference/state_is_chance_node.md)           | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L368) | Returns True if the state represents a chance node, False otherwise.
`is_simultaneous_node()`                     | [Python](api_reference/state_is_simultaneous_node.md)     | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L385) | Returns True if the state represents a simultaneous player node, False otherwise.
`is_terminal()`                              | [Python](api_reference/state_is_terminal.md)              | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L322) | Returns True if the state is terminal (game has finished), False otherwise.
`legal_actions()`                            | [Python](api_reference/state_legal_actions.md)            | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L263) | Returns the list of legal actions for the current player.
`legal_actions(player: int)`                 | [Python](api_reference/state_legal_actions.md)            | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L245) | Returns the list of legal actions for the specified player.
`observation_string()`                       | [Python](api_reference/state_observation_string.md)       | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L516) | Returns a string representing the observation for the current player.
`observation_string(player: int)`            | [Python](api_reference/state_observation_string.md)       | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L516) | Returns a string representing the observation for the specified player.
`observation_tensor()`                       | [Python](api_reference/state_observation_tensor.md)       | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L547) | Returns a list of floats representing the observation for the current player.
`observation_tensor(player: int)`            | [Python](api_reference/state_observation_tensor.md)       | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L547) | Returns a list of floats representing the observation for the specified player.
`returns()`                                  | [Python](api_reference/state_returns.md)                  | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L346) | Returns the list of returns (cumulated reward from the start of the game): one value per player.
`rewards()`                                  | [Python](api_reference/state_rewards.md)                  | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L325) | Returns the list of intermediate rewards (rewards obtained since the last time the player acted): one value per player.
`serialize()`                                | [Python](api_reference/state_serialize.md)                | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L636) | Returns a string representation of the state which can be used to reconstruct the state from the game.

### Game methods

Method                                       | Python                                                                | C++                                                                                                                   | Description
-------------------------------------------- | --------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------- | -----------
`action_to_string(player: int, action: int)` | [Python](api_reference/game_action_to_string.md)                    | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L946) | Returns a (state-independent) string representation of the specified player's action.
`deserialize_state(serialized_data: str)`    | [Python](api_reference/game_deserialize_state.md)                   | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L863) | Reconstructs the state from the serialized state string.
`information_state_tensor_shape()`           | [Python](api_reference/game_information_state_tensor_shape_size.md) | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L815) | Shape that the information state tensor should be perceived as.
`information_state_tensor_size()`            | [Python](api_reference/game_information_state_tensor_shape_size.md) | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L827) | Size of the list (number of values) returned by the state's information state tensor function.
`max_chance_outcomes()`                      | [Python](api_reference/game_max_chance_outcomes.md)                 | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L778) | The maximum number of distinct chance outcomes for chance nodes in the game.
`max_game_length()`                          | [Python](api_reference/game_max_game_length.md)                     | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L873) | The maximum length of any one game (in terms of number of decision nodes visited in the game tree).
`max_utility()`                              | [Python](api_reference/game_max_min_utility.md)                     | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L795) | The maximum achievable utility (return) in over any playing (episode) of the game.
`min_utility()`                              | [Python](api_reference/game_max_min_utility.md)                     | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L795) | The minimum achievable utility (return) in over any playing (episode) of the game.
`new_initial_state()`                        | [Python](api_reference/game_new_initial_state.md)                   | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L764) | Returns a new initial state of the game (note: which might be a chance node).
`num_distinct_actions()`                     | [Python](api_reference/game_num_distinct_actions.md)                | [C++](https://github.com/deepmind/open_spiel/blob/c6fafb92021a8a3aa5f9746cdb79e74917ed26a5/open_spiel/spiel.h#L752) | Returns the number of (state-independent) distinct actions in the game.
`observation_tensor_shape()`                 | [Python](api_reference/game_observation_tensor_shape_size.md)       | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L835) | Shape that the observation tensor should be perceived as.
`observation_tensor_size()`                  | [Python](api_reference/game_observation_tensor_shape_size.md)       | [C++](https://github.com/deepmind/open_spiel/blob/89ba2264a66d9db299108fbd2de4a27b71973f54/open_spiel/spiel.h#L847) | Size of the list (number of values) returned by the state's observation tensor function.
