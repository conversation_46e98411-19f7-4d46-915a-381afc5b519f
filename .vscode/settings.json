{"[python]": {"editor.defaultFormatter": "ms-python.black-formatter"}, "python.formatting.provider": "none", "cmake.ignoreCMakeListsMissing": true, "files.associations": {"__bit_reference": "cpp", "__hash_table": "cpp", "__locale": "cpp", "__node_handle": "cpp", "__split_buffer": "cpp", "__tree": "cpp", "__verbose_abort": "cpp", "array": "cpp", "bitset": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "complex": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "execution": "cpp", "memory": "cpp", "forward_list": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "mutex": "cpp", "new": "cpp", "optional": "cpp", "print": "cpp", "queue": "cpp", "ratio": "cpp", "set": "cpp", "span": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "string": "cpp", "string_view": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "variant": "cpp", "vector": "cpp", "algorithm": "cpp"}}